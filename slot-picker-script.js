/**
 * Valorant Random Slot Picker
 * Created by <PERSON><PERSON><PERSON> (https://www.youtube.com/@Shushie_valorant)
 *
 * A tool for randomly selecting 10 players from 22 available slots for Valorant custom matches.
 * Includes all player slots: Attackers, Defenders, Observers, and Coaches.
 */

document.addEventListener('DOMContentLoaded', () => {
    // DOM Elements
    const pickRandomBtn = document.getElementById('pick-random-btn');
    const resetBtn = document.getElementById('reset-btn');
    const selectionCount = document.getElementById('selection-count');
    const allSlots = document.querySelectorAll('.slot');

    // Game state
    let selectedSlots = new Set();
    let afkSlots = new Set();
    const totalSlots = 22;
    const playersToSelect = 10;

    /**
     * Updates the selection count display
     */
    function updateSelectionCount() {
        const count = selectedSlots.size;
        const afkCount = afkSlots.size;
        const availablePlayers = totalSlots - afkCount;

        if (count === 0) {
            if (afkCount > 0) {
                selectionCount.textContent = `Select ${playersToSelect} players to start the match (${availablePlayers} available, ${afkCount} AFK)`;
            } else {
                selectionCount.textContent = `Select ${playersToSelect} players to start the match`;
            }
            selectionCount.style.color = 'var(--valorant-light)';
        } else if (count === playersToSelect) {
            selectionCount.textContent = `✓ ${count} players selected - Ready to play!`;
            selectionCount.style.color = 'var(--valorant-selected)';
        } else {
            if (afkCount > 0) {
                selectionCount.textContent = `${count}/${playersToSelect} players selected (${availablePlayers} available, ${afkCount} AFK)`;
            } else {
                selectionCount.textContent = `${count}/${playersToSelect} players selected`;
            }
            selectionCount.style.color = 'var(--valorant-red)';
        }
    }

    /**
     * Clears all current selections
     */
    function clearSelections() {
        selectedSlots.clear();
        allSlots.forEach(slot => {
            slot.classList.remove('selected');
            const status = slot.querySelector('.slot-status');
            if (!afkSlots.has(parseInt(slot.dataset.slot))) {
                status.textContent = 'Ready';
            }
        });
        updateSelectionCount();
    }

    /**
     * Resets all player names to default
     */
    function resetPlayerNames() {
        allSlots.forEach((slot, index) => {
            const playerNameElement = slot.querySelector('.player-name');
            if (playerNameElement) {
                playerNameElement.textContent = `Player ${index + 1}`;
            }
        });
    }

    /**
     * Clears all AFK status
     */
    function clearAfkStatus() {
        afkSlots.clear();
        allSlots.forEach(slot => {
            slot.classList.remove('afk');
            const status = slot.querySelector('.slot-status');
            if (!selectedSlots.has(parseInt(slot.dataset.slot))) {
                status.textContent = 'Ready';
            }
        });
        updateSelectionCount();
    }

    /**
     * Toggles AFK status for a slot
     * @param {HTMLElement} slot - The slot element
     */
    function toggleAfkStatus(slot) {
        const slotNumber = parseInt(slot.dataset.slot);
        const status = slot.querySelector('.slot-status');

        if (afkSlots.has(slotNumber)) {
            // Remove from AFK
            afkSlots.delete(slotNumber);
            slot.classList.remove('afk');
            if (selectedSlots.has(slotNumber)) {
                status.textContent = 'Selected';
            } else {
                status.textContent = 'Ready';
            }
        } else {
            // Add to AFK (and remove from selected if needed)
            afkSlots.add(slotNumber);
            selectedSlots.delete(slotNumber);
            slot.classList.remove('selected');
            slot.classList.add('afk');
            status.textContent = 'AFK';
        }

        updateSelectionCount();
    }

    /**
     * Adds selection animation to a slot
     * @param {HTMLElement} slot - The slot element to animate
     * @param {number} delay - Animation delay in milliseconds
     */
    function animateSlotSelection(slot, delay = 0) {
        setTimeout(() => {
            slot.classList.add('selected');
            const status = slot.querySelector('.slot-status');
            status.textContent = 'Selected';

            // Add a brief highlight effect
            slot.style.transform = 'scale(1.05)';
            setTimeout(() => {
                slot.style.transform = 'scale(1)';
            }, 200);
        }, delay);
    }

    /**
     * Picks random slots with animation
     */
    function pickRandomSlots() {
        // Clear previous selections
        clearSelections();

        // Disable button during animation
        pickRandomBtn.disabled = true;
        pickRandomBtn.textContent = 'SELECTING...';

        // Create array of available slot indices (excluding AFK players)
        const availableSlotIndices = [];
        for (let i = 0; i < totalSlots; i++) {
            const slotNumber = parseInt(allSlots[i].dataset.slot);
            if (!afkSlots.has(slotNumber)) {
                availableSlotIndices.push(i);
            }
        }

        // Check if we have enough available players
        if (availableSlotIndices.length < playersToSelect) {
            pickRandomBtn.disabled = false;
            pickRandomBtn.textContent = 'PICK RANDOM 10 PLAYERS';
            alert(`Not enough available players! Need ${playersToSelect} players but only ${availableSlotIndices.length} are available (${afkSlots.size} are AFK).`);
            return;
        }

        // Shuffle the available slots using Fisher-Yates algorithm
        for (let i = availableSlotIndices.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [availableSlotIndices[i], availableSlotIndices[j]] = [availableSlotIndices[j], availableSlotIndices[i]];
        }

        // Select first 10 slots from shuffled available array
        const selectedIndices = availableSlotIndices.slice(0, playersToSelect);

        // Sort selected indices for better visual flow
        selectedIndices.sort((a, b) => a - b);

        // Animate selections with staggered timing
        selectedIndices.forEach((slotIndex, animationIndex) => {
            const slot = allSlots[slotIndex];
            const slotNumber = parseInt(slot.dataset.slot);
            selectedSlots.add(slotNumber);

            // Stagger animations for visual effect
            animateSlotSelection(slot, animationIndex * 150);
        });

        // Update UI after all animations
        setTimeout(() => {
            updateSelectionCount();
            pickRandomBtn.disabled = false;
            pickRandomBtn.textContent = 'PICK RANDOM 10 PLAYERS';

            // Update page title for better UX
            document.title = `${selectedSlots.size} Players Selected - Valorant Random Slot Picker | by Shushie`;

            // Add selected slots as URL parameter for sharing
            const selectedArray = Array.from(selectedSlots).sort((a, b) => a - b);
            const url = new URL(window.location);
            url.searchParams.set('selected', selectedArray.join(','));
            window.history.replaceState({}, '', url);

        }, selectedIndices.length * 150 + 200);
    }

    /**
     * Handles manual slot clicking with AFK support
     * @param {HTMLElement} slot - The clicked slot
     * @param {Event} event - The click event
     */
    function handleSlotClick(slot, event) {
        const slotNumber = parseInt(slot.dataset.slot);

        // Alt+Click toggles AFK status
        if (event && event.altKey) {
            toggleAfkStatus(slot);
            return;
        }

        // Don't allow selection of AFK players
        if (afkSlots.has(slotNumber)) {
            return;
        }

        if (selectedSlots.has(slotNumber)) {
            // Deselect slot
            selectedSlots.delete(slotNumber);
            slot.classList.remove('selected');
            slot.querySelector('.slot-status').textContent = 'Ready';
        } else if (selectedSlots.size < playersToSelect) {
            // Select slot if under limit
            selectedSlots.add(slotNumber);
            animateSlotSelection(slot);
        }

        updateSelectionCount();
    }

    /**
     * Loads selection from URL parameters
     */
    function loadFromURL() {
        const urlParams = new URLSearchParams(window.location.search);
        const selectedParam = urlParams.get('selected');

        if (selectedParam) {
            const selectedArray = selectedParam.split(',').map(num => parseInt(num)).filter(num => !isNaN(num) && num >= 1 && num <= totalSlots);

            if (selectedArray.length <= playersToSelect) {
                clearSelections();

                selectedArray.forEach((slotNumber, index) => {
                    const slot = document.querySelector(`[data-slot="${slotNumber}"]`);
                    if (slot) {
                        selectedSlots.add(slotNumber);
                        animateSlotSelection(slot, index * 100);
                    }
                });

                setTimeout(() => {
                    updateSelectionCount();
                }, selectedArray.length * 100 + 200);
            }
        }
    }

    // Event Listeners
    pickRandomBtn.addEventListener('click', pickRandomSlots);
    resetBtn.addEventListener('click', clearSelections);

    // Add click handlers to all slots for manual selection and AFK toggle
    allSlots.forEach(slot => {
        slot.addEventListener('click', (event) => handleSlotClick(slot, event));
    });

    // Add clear AFK button functionality
    const clearAfkBtn = document.getElementById('clear-afk-btn');
    if (clearAfkBtn) {
        clearAfkBtn.addEventListener('click', clearAfkStatus);
    }

    // Add reset names button functionality
    const resetNamesBtn = document.getElementById('reset-names-btn');
    if (resetNamesBtn) {
        resetNamesBtn.addEventListener('click', resetPlayerNames);
    }

    // Add keyboard shortcuts
    document.addEventListener('keydown', (e) => {
        if (e.code === 'Space' && !pickRandomBtn.disabled) {
            e.preventDefault();
            pickRandomSlots();
        } else if (e.code === 'Escape') {
            clearSelections();
        } else if (e.code === 'KeyR' && !e.ctrlKey && !e.altKey) {
            e.preventDefault();
            resetPlayerNames();
        }
    });

    // Initialize
    updateSelectionCount();
    loadFromURL();

    // Add keyboard shortcuts info to sidebar
    const shortcutsContainer = document.getElementById('shortcuts-container');
    if (shortcutsContainer) {
        const shortcutInfo = document.createElement('div');
        shortcutInfo.className = 'shortcut-box';
        shortcutInfo.innerHTML = `
            <div class="shortcut-title">Keyboard Shortcuts:</div>
            <div class="shortcut-item">SPACE - Random Pick</div>
            <div class="shortcut-item">ESC - Reset Selection</div>
            <div class="shortcut-item">ALT+Click - Toggle AFK</div>
            <div class="shortcut-item">R - Reset Names</div>
        `;
        shortcutsContainer.appendChild(shortcutInfo);
    }

    // Setup player input functionality
    setupPlayerInput();
});

/**
 * Sets up all player input methods
 */
function setupPlayerInput() {
    setupQuickInput();
    setupClipboardInput();
    setupFileInput();
    setupImageInput();
}

/**
 * Sets up the quick text input functionality
 */
function setupQuickInput() {
    const quickInput = document.getElementById('quick-names-input');
    const applyQuickBtn = document.getElementById('apply-quick-names');

    if (!quickInput || !applyQuickBtn) return;

    applyQuickBtn.addEventListener('click', () => {
        const text = quickInput.value.trim();
        if (!text) {
            showUploadStatus('Please enter some player names', 'error');
            return;
        }

        const names = parsePlayerNames(text);
        if (names.length === 0) {
            showUploadStatus('No valid names found', 'error');
            return;
        }

        populateSlots(names);
        showUploadStatus(`Added ${names.length} players`, 'success');
        displayExtractedNames(names);

        // Clear input after successful application
        quickInput.value = '';
    });

    // Allow Enter key to apply names
    quickInput.addEventListener('keydown', (e) => {
        if (e.ctrlKey && e.key === 'Enter') {
            applyQuickBtn.click();
        }
    });
}

/**
 * Sets up clipboard paste functionality
 */
function setupClipboardInput() {
    const clipboardBtn = document.getElementById('clipboard-btn');

    if (!clipboardBtn) return;

    clipboardBtn.addEventListener('click', async () => {
        try {
            const text = await navigator.clipboard.readText();
            if (!text.trim()) {
                showUploadStatus('Clipboard is empty', 'error');
                return;
            }

            const names = parsePlayerNames(text);
            if (names.length === 0) {
                showUploadStatus('No valid names found in clipboard', 'error');
                return;
            }

            populateSlots(names);
            showUploadStatus(`Pasted ${names.length} players from clipboard`, 'success');
            displayExtractedNames(names);

        } catch (error) {
            console.error('Clipboard error:', error);
            showUploadStatus('Could not access clipboard. Try manual paste instead.', 'error');

            // Focus the quick input as fallback
            const quickInput = document.getElementById('quick-names-input');
            if (quickInput) {
                quickInput.focus();
                showUploadStatus('Use Ctrl+V to paste in the text area above', 'error');
            }
        }
    });
}

/**
 * Sets up text file upload functionality
 */
function setupFileInput() {
    const textFileBtn = document.getElementById('text-file-btn');
    const textFileInput = document.getElementById('text-file-upload');

    if (!textFileBtn || !textFileInput) return;

    textFileBtn.addEventListener('click', () => {
        textFileInput.click();
    });

    textFileInput.addEventListener('change', async (event) => {
        const file = event.target.files[0];
        if (!file) return;

        try {
            const text = await file.text();
            const names = parsePlayerNames(text);

            if (names.length === 0) {
                showUploadStatus('No valid names found in file', 'error');
                return;
            }

            populateSlots(names);
            showUploadStatus(`Loaded ${names.length} players from ${file.name}`, 'success');
            displayExtractedNames(names);

        } catch (error) {
            console.error('File reading error:', error);
            showUploadStatus('Could not read file', 'error');
        }
    });
}

/**
 * Sets up image upload with OCR (as fallback option)
 */
function setupImageInput() {
    const imageBtn = document.getElementById('image-btn');
    const imageInput = document.getElementById('image-upload');

    if (!imageBtn || !imageInput) return;

    imageBtn.addEventListener('click', () => {
        imageInput.click();
    });

    imageInput.addEventListener('change', async (event) => {
        const file = event.target.files[0];
        if (!file) return;

        showUploadStatus('OCR is experimental and may not work reliably', 'error');

        // Show the image preview
        const imageUrl = URL.createObjectURL(file);
        const uploadPreview = document.getElementById('upload-preview');
        if (uploadPreview) {
            uploadPreview.innerHTML = `
                <img src="${imageUrl}" alt="Lobby Screenshot" style="max-width: 100%; border-radius: 4px;">
                <div class="extracted-names">
                    <div class="names-title">OCR Alternative:</div>
                    <div class="name-item">1. Look at the image above</div>
                    <div class="name-item">2. Copy names manually to the text area</div>
                    <div class="name-item">3. Click "Add Names" button</div>
                </div>
            `;
        }
    });
}

/**
 * Parses player names from text input
 * @param {string} text - Raw text input
 * @returns {Array} Array of cleaned player names
 */
function parsePlayerNames(text) {
    if (!text || text.trim().length === 0) {
        return [];
    }

    // Split by various delimiters
    const names = text
        .split(/[\n\r,;|\t]+/) // Split by newlines, commas, semicolons, pipes, tabs
        .map(name => name.trim())
        .filter(name => name.length > 0)
        .map(name => {
            // Clean up common prefixes/suffixes
            return name
                .replace(/^[-•*\d+\.\s]+/, '') // Remove bullet points, numbers, etc.
                .replace(/\s*[-:]\s*Ready\s*$/i, '') // Remove "- Ready" suffix
                .replace(/\s*\(\d+ms\)\s*$/i, '') // Remove ping info
                .replace(/^\s*Player\s*\d*\s*[-:]?\s*/i, '') // Remove "Player X:" prefix
                .trim();
        })
        .filter(name => {
            // Filter out invalid names
            if (name.length < 2 || name.length > 20) return false;
            if (/^[0-9\s\-_\.]+$/.test(name)) return false; // Only numbers/symbols
            if (!/[a-zA-Z]/.test(name)) return false; // Must contain at least one letter

            // Skip common UI elements
            const skipWords = [
                'ready', 'afk', 'disconnected', 'connecting', 'loading',
                'attackers', 'defenders', 'observers', 'coach',
                'invite', 'start', 'settings', 'match', 'found',
                'custom', 'game', 'unrated', 'competitive', 'spike', 'rush'
            ];

            return !skipWords.includes(name.toLowerCase());
        });

    // Remove duplicates (case insensitive)
    const uniqueNames = [];
    const seenLower = new Set();

    for (let name of names) {
        const lowerName = name.toLowerCase();
        if (!seenLower.has(lowerName)) {
            seenLower.add(lowerName);
            uniqueNames.push(name);
        }
    }

    return uniqueNames.slice(0, 22); // Limit to reasonable number
}



/**
 * Populates the slots with extracted player names
 * @param {Array} names - Array of player names
 */
function populateSlots(names) {
    const allSlots = document.querySelectorAll('.slot');

    // Clear existing selections first
    clearSelections();

    // Populate slots with names
    names.forEach((name, index) => {
        if (index < allSlots.length) {
            const slot = allSlots[index];
            const playerNameElement = slot.querySelector('.player-name');
            if (playerNameElement) {
                playerNameElement.textContent = name;
            }
        }
    });

    // Update selection count
    updateSelectionCount();
}

/**
 * Displays the extracted names in the upload section
 * @param {Array} names - Array of extracted player names
 */
function displayExtractedNames(names) {
    const uploadPreview = document.getElementById('upload-preview');

    const namesContainer = document.createElement('div');
    namesContainer.className = 'extracted-names';
    namesContainer.innerHTML = `
        <div class="names-title">Extracted Names (${names.length}):</div>
        ${names.map(name => `<div class="name-item">${name}</div>`).join('')}
    `;

    uploadPreview.appendChild(namesContainer);
}

/**
 * Shows upload status with appropriate styling
 * @param {string} message - Status message
 * @param {string} type - Status type (processing, success, error)
 */
function showUploadStatus(message, type = '') {
    const uploadStatus = document.getElementById('upload-status');
    if (uploadStatus) {
        uploadStatus.textContent = message;
        uploadStatus.className = `upload-status ${type}`;
    }
}