/**
 * Valorant Random Slot Picker
 * Created by <PERSON><PERSON><PERSON> (https://www.youtube.com/@Shushie_valorant)
 *
 * A tool for randomly selecting 10 players from 22 available slots for Valorant custom matches.
 * Includes all player slots: Attackers, Defenders, Observers, and Coaches.
 */

document.addEventListener('DOMContentLoaded', () => {
    // DOM Elements
    const pickRandomBtn = document.getElementById('pick-random-btn');
    const resetBtn = document.getElementById('reset-btn');
    const selectionCount = document.getElementById('selection-count');
    const allSlots = document.querySelectorAll('.slot');

    // Game state
    let selectedSlots = new Set();
    let afkSlots = new Set();
    const totalSlots = 22;
    const playersToSelect = 10;

    /**
     * Updates the selection count display
     */
    function updateSelectionCount() {
        const count = selectedSlots.size;
        const afkCount = afkSlots.size;
        const availablePlayers = totalSlots - afkCount;

        if (count === 0) {
            if (afkCount > 0) {
                selectionCount.textContent = `Select ${playersToSelect} players to start the match (${availablePlayers} available, ${afkCount} AFK)`;
            } else {
                selectionCount.textContent = `Select ${playersToSelect} players to start the match`;
            }
            selectionCount.style.color = 'var(--valorant-light)';
        } else if (count === playersToSelect) {
            selectionCount.textContent = `✓ ${count} players selected - Ready to play!`;
            selectionCount.style.color = 'var(--valorant-selected)';
        } else {
            if (afkCount > 0) {
                selectionCount.textContent = `${count}/${playersToSelect} players selected (${availablePlayers} available, ${afkCount} AFK)`;
            } else {
                selectionCount.textContent = `${count}/${playersToSelect} players selected`;
            }
            selectionCount.style.color = 'var(--valorant-red)';
        }
    }

    /**
     * Clears all current selections
     */
    function clearSelections() {
        selectedSlots.clear();
        allSlots.forEach(slot => {
            slot.classList.remove('selected');
            const status = slot.querySelector('.slot-status');
            if (!afkSlots.has(parseInt(slot.dataset.slot))) {
                status.textContent = 'Ready';
            }
        });
        updateSelectionCount();
    }

    /**
     * Resets all player names to default
     */
    function resetPlayerNames() {
        allSlots.forEach((slot, index) => {
            const playerNameElement = slot.querySelector('.player-name');
            if (playerNameElement) {
                playerNameElement.textContent = `Player ${index + 1}`;
            }
        });
    }

    /**
     * Clears all AFK status
     */
    function clearAfkStatus() {
        afkSlots.clear();
        allSlots.forEach(slot => {
            slot.classList.remove('afk');
            const status = slot.querySelector('.slot-status');
            if (!selectedSlots.has(parseInt(slot.dataset.slot))) {
                status.textContent = 'Ready';
            }
        });
        updateSelectionCount();
    }

    /**
     * Toggles AFK status for a slot
     * @param {HTMLElement} slot - The slot element
     */
    function toggleAfkStatus(slot) {
        const slotNumber = parseInt(slot.dataset.slot);
        const status = slot.querySelector('.slot-status');

        if (afkSlots.has(slotNumber)) {
            // Remove from AFK
            afkSlots.delete(slotNumber);
            slot.classList.remove('afk');
            if (selectedSlots.has(slotNumber)) {
                status.textContent = 'Selected';
            } else {
                status.textContent = 'Ready';
            }
        } else {
            // Add to AFK (and remove from selected if needed)
            afkSlots.add(slotNumber);
            selectedSlots.delete(slotNumber);
            slot.classList.remove('selected');
            slot.classList.add('afk');
            status.textContent = 'AFK';
        }

        updateSelectionCount();
    }

    /**
     * Adds selection animation to a slot
     * @param {HTMLElement} slot - The slot element to animate
     * @param {number} delay - Animation delay in milliseconds
     */
    function animateSlotSelection(slot, delay = 0) {
        setTimeout(() => {
            slot.classList.add('selected');
            const status = slot.querySelector('.slot-status');
            status.textContent = 'Selected';

            // Add a brief highlight effect
            slot.style.transform = 'scale(1.05)';
            setTimeout(() => {
                slot.style.transform = 'scale(1)';
            }, 200);
        }, delay);
    }

    /**
     * Picks random slots with animation
     */
    function pickRandomSlots() {
        // Clear previous selections
        clearSelections();

        // Disable button during animation
        pickRandomBtn.disabled = true;
        pickRandomBtn.textContent = 'SELECTING...';

        // Create array of available slot indices (excluding AFK players)
        const availableSlotIndices = [];
        for (let i = 0; i < totalSlots; i++) {
            const slotNumber = parseInt(allSlots[i].dataset.slot);
            if (!afkSlots.has(slotNumber)) {
                availableSlotIndices.push(i);
            }
        }

        // Check if we have enough available players
        if (availableSlotIndices.length < playersToSelect) {
            pickRandomBtn.disabled = false;
            pickRandomBtn.textContent = 'PICK RANDOM 10 PLAYERS';
            alert(`Not enough available players! Need ${playersToSelect} players but only ${availableSlotIndices.length} are available (${afkSlots.size} are AFK).`);
            return;
        }

        // Shuffle the available slots using Fisher-Yates algorithm
        for (let i = availableSlotIndices.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [availableSlotIndices[i], availableSlotIndices[j]] = [availableSlotIndices[j], availableSlotIndices[i]];
        }

        // Select first 10 slots from shuffled available array
        const selectedIndices = availableSlotIndices.slice(0, playersToSelect);

        // Sort selected indices for better visual flow
        selectedIndices.sort((a, b) => a - b);

        // Animate selections with staggered timing
        selectedIndices.forEach((slotIndex, animationIndex) => {
            const slot = allSlots[slotIndex];
            const slotNumber = parseInt(slot.dataset.slot);
            selectedSlots.add(slotNumber);

            // Stagger animations for visual effect
            animateSlotSelection(slot, animationIndex * 150);
        });

        // Update UI after all animations
        setTimeout(() => {
            updateSelectionCount();
            pickRandomBtn.disabled = false;
            pickRandomBtn.textContent = 'PICK RANDOM 10 PLAYERS';

            // Update page title for better UX
            document.title = `${selectedSlots.size} Players Selected - Valorant Random Slot Picker | by Shushie`;

            // Add selected slots as URL parameter for sharing
            const selectedArray = Array.from(selectedSlots).sort((a, b) => a - b);
            const url = new URL(window.location);
            url.searchParams.set('selected', selectedArray.join(','));
            window.history.replaceState({}, '', url);

        }, selectedIndices.length * 150 + 200);
    }

    /**
     * Handles manual slot clicking with AFK support
     * @param {HTMLElement} slot - The clicked slot
     * @param {Event} event - The click event
     */
    function handleSlotClick(slot, event) {
        const slotNumber = parseInt(slot.dataset.slot);

        // Alt+Click toggles AFK status
        if (event && event.altKey) {
            toggleAfkStatus(slot);
            return;
        }

        // Don't allow selection of AFK players
        if (afkSlots.has(slotNumber)) {
            return;
        }

        if (selectedSlots.has(slotNumber)) {
            // Deselect slot
            selectedSlots.delete(slotNumber);
            slot.classList.remove('selected');
            slot.querySelector('.slot-status').textContent = 'Ready';
        } else if (selectedSlots.size < playersToSelect) {
            // Select slot if under limit
            selectedSlots.add(slotNumber);
            animateSlotSelection(slot);
        }

        updateSelectionCount();
    }

    /**
     * Loads selection from URL parameters
     */
    function loadFromURL() {
        const urlParams = new URLSearchParams(window.location.search);
        const selectedParam = urlParams.get('selected');

        if (selectedParam) {
            const selectedArray = selectedParam.split(',').map(num => parseInt(num)).filter(num => !isNaN(num) && num >= 1 && num <= totalSlots);

            if (selectedArray.length <= playersToSelect) {
                clearSelections();

                selectedArray.forEach((slotNumber, index) => {
                    const slot = document.querySelector(`[data-slot="${slotNumber}"]`);
                    if (slot) {
                        selectedSlots.add(slotNumber);
                        animateSlotSelection(slot, index * 100);
                    }
                });

                setTimeout(() => {
                    updateSelectionCount();
                }, selectedArray.length * 100 + 200);
            }
        }
    }

    // Event Listeners
    pickRandomBtn.addEventListener('click', pickRandomSlots);
    resetBtn.addEventListener('click', clearSelections);

    // Add click handlers to all slots for manual selection and AFK toggle
    allSlots.forEach(slot => {
        slot.addEventListener('click', (event) => handleSlotClick(slot, event));
    });

    // Add clear AFK button functionality
    const clearAfkBtn = document.getElementById('clear-afk-btn');
    if (clearAfkBtn) {
        clearAfkBtn.addEventListener('click', clearAfkStatus);
    }

    // Add reset names button functionality
    const resetNamesBtn = document.getElementById('reset-names-btn');
    if (resetNamesBtn) {
        resetNamesBtn.addEventListener('click', resetPlayerNames);
    }

    // Add keyboard shortcuts
    document.addEventListener('keydown', (e) => {
        if (e.code === 'Space' && !pickRandomBtn.disabled) {
            e.preventDefault();
            pickRandomSlots();
        } else if (e.code === 'Escape') {
            clearSelections();
        } else if (e.code === 'KeyR' && !e.ctrlKey && !e.altKey) {
            e.preventDefault();
            resetPlayerNames();
        }
    });

    // Initialize
    updateSelectionCount();
    loadFromURL();

    // Add keyboard shortcuts info to sidebar
    const shortcutsContainer = document.getElementById('shortcuts-container');
    if (shortcutsContainer) {
        const shortcutInfo = document.createElement('div');
        shortcutInfo.className = 'shortcut-box';
        shortcutInfo.innerHTML = `
            <div class="shortcut-title">Keyboard Shortcuts:</div>
            <div class="shortcut-item">SPACE - Random Pick</div>
            <div class="shortcut-item">ESC - Reset Selection</div>
            <div class="shortcut-item">ALT+Click - Toggle AFK</div>
            <div class="shortcut-item">R - Reset Names</div>
        `;
        shortcutsContainer.appendChild(shortcutInfo);
    }

    // Image upload and OCR functionality
    setupImageUpload();
});

/**
 * Sets up the image upload and OCR functionality
 */
function setupImageUpload() {
    const uploadBtn = document.getElementById('upload-btn');
    const fileInput = document.getElementById('lobby-upload');
    const uploadStatus = document.getElementById('upload-status');
    const uploadPreview = document.getElementById('upload-preview');

    if (!uploadBtn || !fileInput || !uploadStatus || !uploadPreview) {
        console.warn('Upload elements not found');
        return;
    }

    // Handle upload button click
    uploadBtn.addEventListener('click', () => {
        fileInput.click();
    });

    // Handle file selection
    fileInput.addEventListener('change', async (event) => {
        const file = event.target.files[0];
        if (!file) return;

        // Validate file type
        if (!file.type.startsWith('image/')) {
            showUploadStatus('Please select an image file', 'error');
            return;
        }

        // Validate file size (max 10MB)
        if (file.size > 10 * 1024 * 1024) {
            showUploadStatus('Image too large. Please select an image under 10MB', 'error');
            return;
        }

        try {
            await processLobbyImage(file);
        } catch (error) {
            console.error('Error processing image:', error);
            showUploadStatus('Error processing image. Please try again.', 'error');
        }
    });
}

/**
 * Processes the uploaded lobby image and extracts player names
 * @param {File} file - The uploaded image file
 */
async function processLobbyImage(file) {
    const uploadStatus = document.getElementById('upload-status');
    const uploadPreview = document.getElementById('upload-preview');
    const uploadBtn = document.getElementById('upload-btn');

    try {
        // Show preview
        const imageUrl = URL.createObjectURL(file);
        uploadPreview.innerHTML = `<img src="${imageUrl}" alt="Lobby Screenshot">`;

        // Disable upload button and show processing status
        uploadBtn.disabled = true;
        showUploadStatus('Processing image...', 'processing');

        // Initialize Tesseract worker with better settings for gaming UI
        const worker = await Tesseract.createWorker('eng');

        // Configure worker for better text recognition
        await worker.setParameters({
            tessedit_char_whitelist: 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789_-#.',
            tessedit_pageseg_mode: Tesseract.PSM.AUTO,
        });

        // Update status
        showUploadStatus('Extracting text from image...', 'processing');

        // Perform OCR with progress tracking
        const { data: { text } } = await worker.recognize(file, {
            logger: m => {
                if (m.status === 'recognizing text') {
                    showUploadStatus(`Processing... ${Math.round(m.progress * 100)}%`, 'processing');
                }
            }
        });

        // Clean up worker
        await worker.terminate();

        // Debug: Show raw OCR text (for troubleshooting)
        console.log('Raw OCR text:', text);

        // Extract player names from the OCR text
        const extractedNames = extractPlayerNames(text);

        if (extractedNames.length > 0) {
            // Populate slots with extracted names
            populateSlots(extractedNames);

            // Show success status
            showUploadStatus(`Found ${extractedNames.length} player names`, 'success');

            // Show extracted names
            displayExtractedNames(extractedNames);
        } else {
            // Show debug info to help troubleshoot
            showUploadStatus('No player names found. Check console for raw text.', 'error');

            // Show raw text for debugging
            const debugContainer = document.createElement('div');
            debugContainer.className = 'extracted-names';
            debugContainer.innerHTML = `
                <div class="names-title">Raw OCR Text (for debugging):</div>
                <div class="name-item" style="white-space: pre-wrap; font-size: 0.7rem;">${text || 'No text detected'}</div>
            `;
            uploadPreview.appendChild(debugContainer);
        }

    } catch (error) {
        console.error('OCR Error:', error);
        showUploadStatus('Failed to process image. Please try again.', 'error');
    } finally {
        // Re-enable upload button
        uploadBtn.disabled = false;
    }
}

/**
 * Extracts player names from OCR text based on Valorant lobby patterns
 * @param {string} text - Raw OCR text
 * @returns {Array} Array of extracted player names
 */
function extractPlayerNames(text) {
    if (!text || text.trim().length === 0) {
        return [];
    }

    // Split by various delimiters and clean up
    const allWords = text
        .split(/[\n\r\t\s]+/)
        .map(word => word.trim())
        .filter(word => word.length > 0);

    const playerNames = [];

    // Known Valorant UI elements to skip (case insensitive)
    const skipWords = new Set([
        'ATTACKERS', 'DEFENDERS', 'OBSERVERS', 'COACH', 'ATTACKERS COACH', 'DEFENDERS COACH',
        'Ready', 'AFK', 'Disconnected', 'Connecting', 'Loading',
        'INVITE', 'START', 'SETTINGS', 'MATCH', 'FOUND', 'LOADING',
        'CUSTOM', 'GAME', 'UNRATED', 'COMPETITIVE', 'SPIKE', 'RUSH',
        'PARTY', 'TEAM', 'QUEUE', 'ms', 'ping'
    ]);

    // Process each word
    for (let word of allWords) {
        // Clean the word - remove common OCR artifacts but keep username characters
        let cleanWord = word
            .replace(/[^\w\-_\.#]/g, '') // Keep alphanumeric, dash, underscore, dot, hash
            .trim();

        // Skip if too short or too long
        if (cleanWord.length < 2 || cleanWord.length > 20) continue;

        // Skip if it's a known UI element (case insensitive)
        if (skipWords.has(cleanWord.toUpperCase())) continue;

        // Skip if it's just numbers
        if (/^\d+$/.test(cleanWord)) continue;

        // Skip if it looks like ping/latency (ends with ms or is just numbers)
        if (/^\d+ms?$/i.test(cleanWord)) continue;

        // Skip if it's very short and all caps (likely UI element)
        if (cleanWord.length <= 3 && cleanWord === cleanWord.toUpperCase()) continue;

        // Must contain at least one letter
        if (!/[a-zA-Z]/.test(cleanWord)) continue;

        // Skip if it's mostly numbers
        const letterCount = (cleanWord.match(/[a-zA-Z]/g) || []).length;
        const numberCount = (cleanWord.match(/\d/g) || []).length;
        if (numberCount > letterCount && letterCount < 2) continue;

        // Add to player names if it looks like a valid username and isn't already included
        if (!playerNames.some(name => name.toLowerCase() === cleanWord.toLowerCase())) {
            playerNames.push(cleanWord);
        }
    }

    // Also try to extract from the original text using a different approach
    // Look for patterns that might be usernames (mixed case, numbers, special chars)
    const usernamePattern = /\b[a-zA-Z][a-zA-Z0-9_\-\.#]{1,19}\b/g;
    const matches = text.match(usernamePattern) || [];

    for (let match of matches) {
        const cleanMatch = match.trim();

        // Skip if already found or if it's a known UI element
        if (playerNames.some(name => name.toLowerCase() === cleanMatch.toLowerCase())) continue;
        if (skipWords.has(cleanMatch.toUpperCase())) continue;
        if (/^\d+$/.test(cleanMatch)) continue;
        if (/^\d+ms?$/i.test(cleanMatch)) continue;

        // Add if it looks like a username
        if (cleanMatch.length >= 2 && cleanMatch.length <= 20) {
            playerNames.push(cleanMatch);
        }
    }

    // Remove duplicates (case insensitive) and sort by length
    const uniqueNames = [];
    const seenLower = new Set();

    for (let name of playerNames) {
        const lowerName = name.toLowerCase();
        if (!seenLower.has(lowerName)) {
            seenLower.add(lowerName);
            uniqueNames.push(name);
        }
    }

    // Sort by length (longer names are typically more accurate)
    uniqueNames.sort((a, b) => b.length - a.length);

    // Limit to reasonable number of players
    return uniqueNames.slice(0, 22);
}

/**
 * Populates the slots with extracted player names
 * @param {Array} names - Array of player names
 */
function populateSlots(names) {
    const allSlots = document.querySelectorAll('.slot');

    // Clear existing selections first
    clearSelections();

    // Populate slots with names
    names.forEach((name, index) => {
        if (index < allSlots.length) {
            const slot = allSlots[index];
            const playerNameElement = slot.querySelector('.player-name');
            if (playerNameElement) {
                playerNameElement.textContent = name;
            }
        }
    });

    // Update selection count
    updateSelectionCount();
}

/**
 * Displays the extracted names in the upload section
 * @param {Array} names - Array of extracted player names
 */
function displayExtractedNames(names) {
    const uploadPreview = document.getElementById('upload-preview');

    const namesContainer = document.createElement('div');
    namesContainer.className = 'extracted-names';
    namesContainer.innerHTML = `
        <div class="names-title">Extracted Names (${names.length}):</div>
        ${names.map(name => `<div class="name-item">${name}</div>`).join('')}
    `;

    uploadPreview.appendChild(namesContainer);
}

/**
 * Shows upload status with appropriate styling
 * @param {string} message - Status message
 * @param {string} type - Status type (processing, success, error)
 */
function showUploadStatus(message, type = '') {
    const uploadStatus = document.getElementById('upload-status');
    if (uploadStatus) {
        uploadStatus.textContent = message;
        uploadStatus.className = `upload-status ${type}`;
    }
}